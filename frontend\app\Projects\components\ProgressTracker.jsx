import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Alert,
    ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ThemeContext } from '../../../context/ThemeContext';
import { AuthContext } from '../../../context/AuthContext';
import { addProgressLog, fetchProgressLogs, updateProgressLog, deleteProgressLog } from '../../../api/projects/projectApi';

const ProgressTracker = ({ projectId, project }) => {
    const { theme } = useContext(ThemeContext);
    const { user } = useContext(AuthContext);
    const queryClient = useQueryClient();

    const [isAddingProgress, setIsAddingProgress] = useState(false);
    const [editingLogId, setEditingLogId] = useState(null);
    const [newProgress, setNewProgress] = useState({ stage: '', description: '' });

    // Fetch progress logs
    const {
        data: progressData,
        isLoading,
        error,
    } = useQuery({
        queryKey: ['progressLogs', projectId],
        queryFn: () => fetchProgressLogs(projectId),
    });

    // Add progress mutation
    const addProgressMutation = useMutation({
        mutationFn: (progressData) => addProgressLog(projectId, progressData),
        onSuccess: () => {
            queryClient.invalidateQueries(['progressLogs', projectId]);
            setIsAddingProgress(false);
            setNewProgress({ stage: '', description: '' });
            Alert.alert('Success', 'Progress log added successfully');
        },
        onError: (error) => {
            Alert.alert('Error', error.message);
        },
    });

    // Update progress mutation
    const updateProgressMutation = useMutation({
        mutationFn: ({ logId, progressData }) => updateProgressLog(projectId, logId, progressData),
        onSuccess: () => {
            queryClient.invalidateQueries(['progressLogs', projectId]);
            setEditingLogId(null);
            Alert.alert('Success', 'Progress log updated successfully');
        },
        onError: (error) => {
            Alert.alert('Error', error.message);
        },
    });

    // Delete progress mutation
    const deleteProgressMutation = useMutation({
        mutationFn: (logId) => deleteProgressLog(projectId, logId),
        onSuccess: () => {
            queryClient.invalidateQueries(['progressLogs', projectId]);
            Alert.alert('Success', 'Progress log deleted successfully');
        },
        onError: (error) => {
            Alert.alert('Error', error.message);
        },
    });

    const handleAddProgress = () => {
        if (!newProgress.stage.trim()) {
            Alert.alert('Error', 'Please enter a stage');
            return;
        }
        addProgressMutation.mutate(newProgress);
    };

    const handleUpdateProgress = (logId, updatedData) => {
        updateProgressMutation.mutate({ logId, progressData: updatedData });
    };

    const handleDeleteProgress = (logId) => {
        Alert.alert(
            'Confirm Delete',
            'Are you sure you want to delete this progress log?',
            [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Delete', style: 'destructive', onPress: () => deleteProgressMutation.mutate(logId) },
            ]
        );
    };

    const isContractor = user?.role === 'contractor';
    const isAssignedContractor = isContractor && project?.contractorId?.toString() === user?.id?.toString();

    // Use project's userRoleInProject as the authoritative source for contractor access
    const isAssignedContractorFallback = project?.userRoleInProject === 'contractor';
    const shouldShowButton = isAssignedContractor || isAssignedContractorFallback;

    if (isLoading) {
        return (
            <View style={[styles.container, { backgroundColor: theme.CARD }]}>
                <ActivityIndicator size="large" color={theme.PRIMARY} />
                <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
                    Loading progress...
                </Text>
            </View>
        );
    }

    if (error) {
        return (
            <View style={[styles.container, { backgroundColor: theme.CARD }]}>
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    Failed to load progress logs
                </Text>
            </View>
        );
    }

    const progressLogs = progressData?.progressLogs || [];

    return (
        <View style={[styles.container, { backgroundColor: theme.CARD }]}>
            <View style={styles.header}>
                <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
                    Project Progress
                </Text>
                {shouldShowButton && (
                    <TouchableOpacity
                        style={[styles.addButton, { backgroundColor: theme.PRIMARY }]}
                        onPress={() => setIsAddingProgress(true)}
                    >
                        <Ionicons name="add" size={20} color={theme.WHITE} />
                    </TouchableOpacity>
                )}
            </View>

            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                {/* Add new progress form */}
                {isAddingProgress && (
                    <View style={[styles.progressForm, { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER }]}>
                        <TextInput
                            style={[styles.input, { backgroundColor: theme.WHITE, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }]}
                            placeholder="Stage (e.g., Foundation, Framing, Roofing)"
                            placeholderTextColor={theme.TEXT_SECONDARY}
                            value={newProgress.stage}
                            onChangeText={(text) => setNewProgress({ ...newProgress, stage: text })}
                        />
                        <TextInput
                            style={[styles.textArea, { backgroundColor: theme.WHITE, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }]}
                            placeholder="Description (optional)"
                            placeholderTextColor={theme.TEXT_SECONDARY}
                            value={newProgress.description}
                            onChangeText={(text) => setNewProgress({ ...newProgress, description: text })}
                            multiline
                            numberOfLines={3}
                        />
                        <View style={styles.formButtons}>
                            <TouchableOpacity
                                style={[styles.cancelButton, { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER }]}
                                onPress={() => {
                                    setIsAddingProgress(false);
                                    setNewProgress({ stage: '', description: '' });
                                }}
                            >
                                <Text style={[styles.cancelButtonText, { color: theme.TEXT_SECONDARY }]}>Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.saveButton, { backgroundColor: theme.PRIMARY }]}
                                onPress={handleAddProgress}
                                disabled={addProgressMutation.isPending}
                            >
                                {addProgressMutation.isPending ? (
                                    <ActivityIndicator size="small" color={theme.WHITE} />
                                ) : (
                                    <Text style={[styles.saveButtonText, { color: theme.WHITE }]}>Add Progress</Text>
                                )}
                            </TouchableOpacity>
                        </View>
                    </View>
                )}

                {/* Progress timeline */}
                {progressLogs.length === 0 ? (
                    <View style={styles.emptyState}>
                        <Ionicons name="time-outline" size={48} color={theme.TEXT_SECONDARY} />
                        <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
                            No progress logs yet
                        </Text>
                        {shouldShowButton && (
                            <Text style={[styles.emptySubtext, { color: theme.TEXT_SECONDARY }]}>
                                Add your first progress update
                            </Text>
                        )}
                    </View>
                ) : (
                    <View style={styles.timeline}>
                        {progressLogs.map((log, index) => (
                            <ProgressLogItem
                                key={log._id}
                                log={log}
                                isLast={index === progressLogs.length - 1}
                                theme={theme}
                                isContractor={isAssignedContractor}
                                currentUserId={user?.id}
                                onEdit={handleUpdateProgress}
                                onDelete={handleDeleteProgress}
                                isEditing={editingLogId === log._id}
                                setEditing={setEditingLogId}
                                isUpdating={updateProgressMutation.isPending}
                            />
                        ))}
                    </View>
                )}
            </ScrollView>
        </View>
    );
};

const ProgressLogItem = ({
    log,
    isLast,
    theme,
    isContractor,
    currentUserId,
    onEdit,
    onDelete,
    isEditing,
    setEditing,
    isUpdating
}) => {
    const [editData, setEditData] = useState({ stage: log.stage, description: log.description || '' });

    const canEdit = isContractor && log.addedById._id === currentUserId;
    const formattedDate = new Date(log.date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });

    const handleSaveEdit = () => {
        if (!editData.stage.trim()) {
            Alert.alert('Error', 'Please enter a stage');
            return;
        }
        onEdit(log._id, editData);
    };

    return (
        <View style={styles.timelineItem}>
            <View style={styles.timelineLeft}>
                <View style={[styles.timelineDot, { backgroundColor: theme.PRIMARY }]} />
                {!isLast && <View style={[styles.timelineLine, { backgroundColor: theme.BORDER }]} />}
            </View>
            <View style={[styles.timelineContent, { backgroundColor: theme.WHITE, borderColor: theme.BORDER }]}>
                {isEditing ? (
                    <View style={styles.editForm}>
                        <TextInput
                            style={[styles.editInput, { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }]}
                            value={editData.stage}
                            onChangeText={(text) => setEditData({ ...editData, stage: text })}
                            placeholder="Stage"
                            placeholderTextColor={theme.TEXT_SECONDARY}
                        />
                        <TextInput
                            style={[styles.editTextArea, { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }]}
                            value={editData.description}
                            onChangeText={(text) => setEditData({ ...editData, description: text })}
                            placeholder="Description"
                            placeholderTextColor={theme.TEXT_SECONDARY}
                            multiline
                            numberOfLines={2}
                        />
                        <View style={styles.editButtons}>
                            <TouchableOpacity
                                style={[styles.editCancelButton, { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER }]}
                                onPress={() => {
                                    setEditing(null);
                                    setEditData({ stage: log.stage, description: log.description || '' });
                                }}
                            >
                                <Text style={[styles.editCancelText, { color: theme.TEXT_SECONDARY }]}>Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.editSaveButton, { backgroundColor: theme.PRIMARY }]}
                                onPress={handleSaveEdit}
                                disabled={isUpdating}
                            >
                                {isUpdating ? (
                                    <ActivityIndicator size="small" color={theme.WHITE} />
                                ) : (
                                    <Text style={[styles.editSaveText, { color: theme.WHITE }]}>Save</Text>
                                )}
                            </TouchableOpacity>
                        </View>
                    </View>
                ) : (
                    <>
                        <View style={styles.logHeader}>
                            <Text style={[styles.logStage, { color: theme.TEXT_PRIMARY }]}>{log.stage}</Text>
                            {canEdit && (
                                <View style={styles.logActions}>
                                    <TouchableOpacity
                                        style={styles.actionButton}
                                        onPress={() => setEditing(log._id)}
                                    >
                                        <Ionicons name="pencil" size={16} color={theme.TEXT_SECONDARY} />
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={styles.actionButton}
                                        onPress={() => onDelete(log._id)}
                                    >
                                        <Ionicons name="trash" size={16} color={theme.ERROR} />
                                    </TouchableOpacity>
                                </View>
                            )}
                        </View>
                        {log.description && (
                            <Text style={[styles.logDescription, { color: theme.TEXT_SECONDARY }]}>
                                {log.description}
                            </Text>
                        )}
                        <View style={styles.logFooter}>
                            <Text style={[styles.logDate, { color: theme.TEXT_SECONDARY }]}>{formattedDate}</Text>
                            <Text style={[styles.logAuthor, { color: theme.TEXT_SECONDARY }]}>
                                by {log.addedById.name}
                            </Text>
                        </View>
                    </>
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        borderRadius: 12,
        margin: 16,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    addButton: {
        width: 36,
        height: 36,
        borderRadius: 18,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scrollView: {
        flex: 1,
    },
    progressForm: {
        padding: 16,
        borderRadius: 8,
        borderWidth: 1,
        marginBottom: 16,
    },
    input: {
        borderWidth: 1,
        borderRadius: 8,
        padding: 12,
        marginBottom: 12,
        fontSize: 16,
    },
    textArea: {
        borderWidth: 1,
        borderRadius: 8,
        padding: 12,
        marginBottom: 12,
        fontSize: 16,
        minHeight: 80,
        textAlignVertical: 'top',
    },
    formButtons: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        gap: 12,
    },
    cancelButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 6,
        borderWidth: 1,
    },
    cancelButtonText: {
        fontSize: 14,
        fontWeight: '500',
    },
    saveButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 6,
        minWidth: 100,
        alignItems: 'center',
    },
    saveButtonText: {
        fontSize: 14,
        fontWeight: '500',
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 48,
    },
    emptyText: {
        fontSize: 16,
        fontWeight: '500',
        marginTop: 12,
    },
    emptySubtext: {
        fontSize: 14,
        marginTop: 4,
    },
    timeline: {
        paddingLeft: 8,
    },
    timelineItem: {
        flexDirection: 'row',
        marginBottom: 16,
    },
    timelineLeft: {
        alignItems: 'center',
        marginRight: 16,
    },
    timelineDot: {
        width: 12,
        height: 12,
        borderRadius: 6,
        marginTop: 4,
    },
    timelineLine: {
        width: 2,
        flex: 1,
        marginTop: 8,
    },
    timelineContent: {
        flex: 1,
        padding: 16,
        borderRadius: 8,
        borderWidth: 1,
    },
    logHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    logStage: {
        fontSize: 16,
        fontWeight: '600',
        flex: 1,
    },
    logActions: {
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        padding: 4,
    },
    logDescription: {
        fontSize: 14,
        lineHeight: 20,
        marginBottom: 12,
    },
    logFooter: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    logDate: {
        fontSize: 12,
    },
    logAuthor: {
        fontSize: 12,
        fontStyle: 'italic',
    },
    editForm: {
        gap: 12,
    },
    editInput: {
        borderWidth: 1,
        borderRadius: 6,
        padding: 10,
        fontSize: 14,
    },
    editTextArea: {
        borderWidth: 1,
        borderRadius: 6,
        padding: 10,
        fontSize: 14,
        minHeight: 60,
        textAlignVertical: 'top',
    },
    editButtons: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        gap: 8,
    },
    editCancelButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
        borderWidth: 1,
    },
    editCancelText: {
        fontSize: 12,
        fontWeight: '500',
    },
    editSaveButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
        minWidth: 60,
        alignItems: 'center',
    },
    editSaveText: {
        fontSize: 12,
        fontWeight: '500',
    },
    loadingText: {
        marginTop: 12,
        fontSize: 16,
    },
    errorText: {
        fontSize: 16,
        textAlign: 'center',
    },
});

export default ProgressTracker;
