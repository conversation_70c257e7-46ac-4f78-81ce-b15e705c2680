import React, { useContext } from 'react';
import {
    View,
    Text,
    StyleSheet,
    SafeAreaView,
    ScrollView,
    ActivityIndicator,
    RefreshControl,
    TouchableOpacity,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import { fetchServiceRequests } from '../../api/serviceRequests/serviceRequestApi';
import ServiceRequestCard from '../Components/ServiceRequests/ServiceRequestCard';
import BackButton from '../Components/Shared/BackButton';
import { showToast } from '../../utils/showToast';

export default function ServiceRequests() {
    const { theme, isDarkMode } = useContext(ThemeContext);

    const {
        data: serviceRequestsData,
        isLoading,
        isError,
        error,
        refetch,
        isRefetching,
    } = useQuery({
        queryKey: ['serviceRequests'],
        queryFn: fetchServiceRequests,
        retry: 2,
        onError: (error) => {
            showToast('error', 'Error', error.message || 'Failed to fetch service requests');
        },
    });

    const serviceRequests = serviceRequestsData?.serviceRequests || [];

    const EmptyState = () => (
        <View style={styles.emptyContainer}>
            <Ionicons
                name="mail-outline"
                size={80}
                color={theme.TEXT_SECONDARY}
            />
            <Text style={[styles.emptyTitle, { color: theme.TEXT_PRIMARY }]}>
                No Service Requests
            </Text>
            <Text style={[styles.emptySubtitle, { color: theme.TEXT_SECONDARY }]}>
                You haven't received any service requests yet. When clients send you requests, they'll appear here.
            </Text>
        </View>
    );

    const ErrorState = () => (
        <View style={styles.errorContainer}>
            <Ionicons
                name="alert-circle-outline"
                size={80}
                color={theme.ERROR}
            />
            <Text style={[styles.errorTitle, { color: theme.TEXT_PRIMARY }]}>
                Failed to Load
            </Text>
            <Text style={[styles.errorSubtitle, { color: theme.TEXT_SECONDARY }]}>
                {error?.message || 'Something went wrong while fetching service requests.'}
            </Text>
            <TouchableOpacity
                style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
                onPress={refetch}
            >
                <Text style={[styles.retryButtonText, { color: theme.BACKGROUND }]}>
                    Try Again
                </Text>
            </TouchableOpacity>
        </View>
    );

    const LoadingState = () => (
        <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.PRIMARY} />
            <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
                Loading service requests...
            </Text>
        </View>
    );

    return (
        <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <View style={styles.header}>
                <BackButton />
                <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>
                    Service Requests
                </Text>
                <View style={styles.headerRight} />
            </View>

            <ScrollView
                style={styles.content}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={isRefetching}
                        onRefresh={refetch}
                        colors={[theme.PRIMARY]}
                        tintColor={theme.PRIMARY}
                    />
                }
            >
                {isLoading ? (
                    <LoadingState />
                ) : isError ? (
                    <ErrorState />
                ) : serviceRequests.length === 0 ? (
                    <EmptyState />
                ) : (
                    <View style={styles.requestsList}>
                        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                            Pending Requests ({serviceRequests.length})
                        </Text>
                        <Text style={[styles.sectionSubtitle, { color: theme.TEXT_SECONDARY }]}>
                            Review and respond to client requests
                        </Text>
                        
                        {serviceRequests.map((request) => (
                            <ServiceRequestCard
                                key={request._id}
                                serviceRequest={request}
                            />
                        ))}
                    </View>
                )}
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(0,0,0,0.1)',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: '600',
    },
    headerRight: {
        width: 40,
    },
    content: {
        flex: 1,
        paddingHorizontal: 20,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 20,
        marginBottom: 4,
    },
    sectionSubtitle: {
        fontSize: 14,
        marginBottom: 16,
    },
    requestsList: {
        paddingBottom: 20,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
        paddingHorizontal: 40,
    },
    emptyTitle: {
        fontSize: 24,
        fontWeight: '600',
        marginTop: 20,
        marginBottom: 8,
        textAlign: 'center',
    },
    emptySubtitle: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
        paddingHorizontal: 40,
    },
    errorTitle: {
        fontSize: 24,
        fontWeight: '600',
        marginTop: 20,
        marginBottom: 8,
        textAlign: 'center',
    },
    errorSubtitle: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
        marginBottom: 24,
    },
    retryButton: {
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 8,
    },
    retryButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    loadingText: {
        fontSize: 16,
        marginTop: 16,
    },
});
