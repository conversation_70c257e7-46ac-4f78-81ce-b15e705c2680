const { body, param, query, header } = require('express-validator');

// Common regex patterns
const REGEX_PATTERNS = {
    NAME: /^[a-zA-Z\s]+$/,
    NAME_WITH_DOTS: /^[a-zA-Z\s.]+$/,
    PHONE: /^[6-9]\d{9}$/,
    PAN: /^[A-Z]{3}[ABCFGHLJPT][A-Z][0-9]{4}[A-Z]$/,
    AADHAAR: /^\d{12}$/,
    PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z0-9\s])[^\s]{8,}$/,
};

// Reusable field validators
const validators = {
    // Basic field validators
    name: (field = 'name', optional = false) =>
        body(field)
            .if((value, { req }) => !optional || req.body[field])
            .trim()
            .notEmpty()
            .withMessage(`${field} is required`)
            .isLength({ min: 2, max: 50 })
            .withMessage(`${field} must be between 2 and 50 characters`)
            .matches(REGEX_PATTERNS.NAME)
            .withMessage(`${field} can only contain letters and spaces`)
            .escape(),

    email: (optional = false) =>
        body('email')
            .if((value, { req }) => !optional || req.body.email)
            .trim()
            .notEmpty()
            .withMessage('Email is required')
            .isEmail()
            .withMessage('Please provide a valid email address')
            .normalizeEmail()
            .escape(),

    phone: (optional = false) =>
        body('phone')
            .if((value, { req }) => !optional || req.body.phone)
            .trim()
            .notEmpty()
            .withMessage('Phone number is required')
            .matches(REGEX_PATTERNS.PHONE)
            .withMessage('Please provide a valid 10-digit Indian phone number')
            .escape(),

    password: (field = 'password', minLength = 8) =>
        body(field)
            .notEmpty()
            .withMessage(`${field} is required`)
            .isLength({ min: minLength })
            .withMessage(
                `${field} must be at least ${minLength} characters long`
            )
            .matches(REGEX_PATTERNS.PASSWORD)
            .withMessage(
                `${field} must contain at least one uppercase letter, one lowercase letter, one digit, and one special character`
            )
            .escape(),

    newPasswordWithDifference: () =>
        body('newPassword')
            .notEmpty()
            .withMessage('New password is required')
            .isLength({ min: 8 })
            .withMessage('New password must be at least 8 characters long')
            .matches(REGEX_PATTERNS.PASSWORD)
            .withMessage(
                'New password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character'
            )
            .custom((value, { req }) => {
                if (value === req.body.currentPassword) {
                    throw new Error(
                        'New password must be different from current password'
                    );
                }
                return true;
            })
            .escape(),

    confirmPassword: () =>
        body('confirmPassword')
            .notEmpty()
            .withMessage('Confirm password is required')
            .custom((value, { req }) => {
                if (value !== req.body.newPassword) {
                    throw new Error('Passwords do not match');
                }
                return true;
            })
            .escape(),

    aadhaarNumber: (optional = false) => {
        let validator = body('aadhaarNumber');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .trim()
            .notEmpty()
            .withMessage('Aadhaar number is required')
            .matches(REGEX_PATTERNS.AADHAAR)
            .withMessage('Aadhaar number must be exactly 12 digits')
            .escape();
    },

    nameOnAadhaar: (optional = false) => {
        let validator = body('nameOnAadhaar');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .trim()
            .notEmpty()
            .withMessage('Name on Aadhaar is required')
            .isLength({ min: 2, max: 100 })
            .withMessage('Name on Aadhaar must be between 2 and 100 characters')
            .matches(REGEX_PATTERNS.NAME_WITH_DOTS)
            .withMessage(
                'Name on Aadhaar can only contain letters, spaces, and dots'
            )
            .escape();
    },

    dateOfBirth: (field = 'dateOfBirth', optional = false) => {
        let validator = body(field);
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .notEmpty()
            .withMessage('Date of birth is required')
            .isISO8601()
            .withMessage('Date of birth must be a valid date')
            .custom((value) => {
                const birthDate = new Date(value);
                const today = new Date();
                const age = today.getFullYear() - birthDate.getFullYear();
                if (age < 18 || age > 100) {
                    throw new Error('Age must be between 18 and 100 years');
                }
                return true;
            });
    },

    gender: (optional = false) => {
        let validator = body('gender');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .trim()
            .notEmpty()
            .withMessage('Gender is required')
            .isIn(['Male', 'Female', 'Other'])
            .withMessage('Gender must be Male, Female, or Other')
            .escape();
    },

    address: (optional = false) => {
        let validator = body('address');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .trim()
            .notEmpty()
            .withMessage('Address is required')
            .isLength({ min: 10, max: 500 })
            .withMessage('Address must be between 10 and 500 characters')
            .escape();
    },

    panNumber: (optional = false) => {
        let validator = body('panNumber');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .trim()
            .notEmpty()
            .withMessage('PAN number is required')
            .matches(REGEX_PATTERNS.PAN)
            .withMessage(
                'PAN number must be in valid format (e.g., **********)'
            )
            .escape();
    },

    panName: (optional = false) => {
        let validator = body('panName');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .trim()
            .notEmpty()
            .withMessage('Name on PAN is required')
            .isLength({ min: 2, max: 100 })
            .withMessage('Name on PAN must be between 2 and 100 characters')
            .matches(REGEX_PATTERNS.NAME_WITH_DOTS)
            .withMessage(
                'Name on PAN can only contain letters, spaces, and dots'
            )
            .escape();
    },

    assetType: (location = 'body') =>
        (location === 'body' ? body : query)('assetType')
            .trim()
            .notEmpty()
            .withMessage('Asset type is required')
            .isIn(['aadhar', 'pan', 'avatar', 'portfolio'])
            .withMessage('Asset type must be aadhar, pan, avatar, or portfolio')
            .escape(),

    optionalAssetType: () =>
        query('assetType')
            .optional()
            .isIn(['aadhar', 'pan', 'avatar', 'portfolio'])
            .withMessage(
                'Asset type must be aadhar, pan, avatar, or portfolio'
            ),

    entityType: (location = 'body') =>
        (location === 'body' ? body : query)('entityType')
            .trim()
            .notEmpty()
            .withMessage('Entity type is required')
            .isIn([
                'User',
                'ContractorProfile',
                'BrokerProfile',
                'Aadhaar',
                'PAN',
            ])
            .withMessage(
                'Entity type must be User, ContractorProfile, BrokerProfile, Aadhaar, or PAN'
            )
            .escape(),

    mongoId: (field = 'id', location = 'param') =>
        (location === 'param' ? param : body)(field)
            .notEmpty()
            .withMessage(`${field} is required`)
            .isMongoId()
            .withMessage(`Invalid ${field} format`)
            .escape(),

    portfolioCaption: (optional = false) => {
        let validator = body('caption');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .trim()
            .notEmpty()
            .withMessage('Caption is required')
            .isLength({ min: 1, max: 500 })
            .withMessage('Caption must be between 1 and 500 characters')
            .escape();
    },

    locations: () =>
        body('location')
            .optional()
            .isArray()
            .withMessage('Location must be an array')
            .custom((locations) => {
                if (locations) {
                    if (locations.length > 5) {
                        throw new Error('Maximum 5 locations allowed');
                    }
                    const allValid = locations.every(
                        (location) =>
                            typeof location === 'string' &&
                            location.trim().length > 0
                    );
                    if (!allValid) {
                        throw new Error(
                            'Each location must be a non-empty string'
                        );
                    }
                }
                return true;
            }),

    serviceAreas: (maxAreas = 10, optional = false) => {
        let validator = body('serviceAreas');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .customSanitizer((value) => {
                if (typeof value === 'string') {
                    return [value];
                }
                return value;
            })
            .isArray()
            .withMessage('Service areas must be an array')
            .custom((areas) => {
                if (areas && areas.length > 0) {
                    if (areas.length > maxAreas) {
                        throw new Error(
                            `Maximum ${maxAreas} service areas allowed`
                        );
                    }
                    const allValid = areas.every(
                        (area) =>
                            typeof area === 'string' &&
                            area.trim().length > 0 &&
                            area.length <= 100
                    );
                    if (!allValid) {
                        throw new Error(
                            'Each service area must be a non-empty string and less than or equal to 100 characters'
                        );
                    }
                }
                return true;
            });
    },

    specialties: (maxSpecialties = 15, optional = true) => {
        let validator = body('specialties');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .customSanitizer((value) => {
                if (typeof value === 'string') {
                    return [value];
                }
                return value;
            })
            .isArray()
            .withMessage('Specialties must be an array')
            .custom((specialties) => {
                if (specialties && specialties.length > 0) {
                    if (specialties.length > maxSpecialties) {
                        throw new Error(
                            `Maximum ${maxSpecialties} specialties allowed`
                        );
                    }
                    const allValid = specialties.every(
                        (specialty) =>
                            typeof specialty === 'string' &&
                            specialty.trim().length > 0 &&
                            specialty.length <= 100
                    );
                    if (!allValid) {
                        throw new Error(
                            'Each specialty must be a non-empty string and less than or equal to 100 characters'
                        );
                    }
                }
                return true;
            });
    },

    portfolio: (optional = true) => {
        let validator = body('portfolio');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .isArray()
            .withMessage('Portfolio must be an array')
            .custom((portfolio) => {
                if (portfolio && portfolio.length > 0) {
                    if (portfolio.length > 10) {
                        throw new Error('Maximum 10 portfolio items allowed');
                    }
                    const allValid = portfolio.every(
                        (item) =>
                            typeof item === 'string' &&
                            item.trim().length > 0 &&
                            item.length <= 500
                    );
                    if (!allValid) {
                        throw new Error(
                            'Each portfolio item must be a non-empty string and less than or equal to 500 characters'
                        );
                    }
                }
                return true;
            });
    },

    experience: (optional = false) => {
        let validator = body('experience');
        if (optional) {
            validator = validator.optional({ checkFalsy: true });
        }
        return validator
            .isInt({ min: 0, max: 50 })
            .withMessage('Experience must be a number between 0 and 50 years');
    },

    fileType: () =>
        body('fileType')
            .trim()
            .notEmpty()
            .withMessage('File type is required')
            .isIn(['png', 'jpeg', 'jpg', 'pdf'])
            .withMessage('File type must be png, jpeg, jpg, or pdf')
            .escape(),

    token: () =>
        body('token')
            .trim()
            .notEmpty()
            .withMessage('Reset token is required')
            .isLength({ min: 10 })
            .withMessage('Invalid reset token')
            .escape(),

    sessionHeader: () =>
        header('session')
            .notEmpty()
            .withMessage('Session header is required')
            .isLength({ min: 1 })
            .withMessage('Session header cannot be empty'),
};

// Authentication validators
const userSignupValidators = [
    validators.name(),
    validators.email(),
    validators.phone(),
    validators.password(),
];

const userLoginValidators = [validators.email(), validators.password()];

const refreshTokenValidator = [validators.sessionHeader()];

// Profile validators
const updateProfileValidators = [
    validators.name('name', true),
    validators.email(true),
    validators.phone(true),
    validators.locations(),
];

// Broker and Contractor shared validators
const aadhaarValidators = [
    validators.aadhaarNumber(),
    validators.nameOnAadhaar(),
    validators.dateOfBirth(),
    validators.gender(),
    validators.address(),
];

const panValidators = [
    validators.panNumber(),
    validators.panName(),
    validators.dateOfBirth('panDateOfBirth'),
];

// Broker application validators
const brokerApplicationValidators = [
    ...aadhaarValidators,
    ...panValidators,
    validators.experience(),
    validators.serviceAreas(),
];

// Contractor application validators
const contractorApplicationValidators = [
    ...aadhaarValidators,
    ...panValidators,
    validators.serviceAreas(),
    validators.specialties(),
    validators.experience(),
];

// Asset validators
const assetValidators = [
    validators.assetType(),
    validators.fileType(),
    validators
        .name('Name', true)
        .isLength({ max: 100 })
        .withMessage('Name must be less than 100 characters'),
];

const createAssetValidators = [
    validators.mongoId('entityID', 'body'),
    validators.entityType(),
    validators.assetType(),
];

const getAssetsByEntityValidators = [
    validators.mongoId('entityID', 'query'),
    validators.entityType('query'),
    validators.optionalAssetType(),
];

// Password reset validators
const forgotPasswordValidators = [validators.email()];

const resetPasswordValidators = [
    validators.token(),
    validators.password('newPassword'),
    validators.confirmPassword(),
];

const changePasswordValidators = [
    body('currentPassword')
        .notEmpty()
        .withMessage('Current password is required')
        .escape(),
    validators.newPasswordWithDifference(),
    validators.confirmPassword(),
];

// Broker update validators
const brokerUpdateValidators = [
    validators.mongoId('brokerId'),
    validators.aadhaarNumber(false),
    validators.nameOnAadhaar(false),
    validators.dateOfBirth('dateOfBirth', false),
    validators.gender(false),
    validators.address(false),
    validators.panNumber(false),
    validators.panName(false),
    validators.dateOfBirth('panDateOfBirth', false),
    validators.experience(false),
    validators.serviceAreas(10, false),
];

// Contractor update validators
const contractorUpdateValidators = [
    validators.mongoId('contractorId'),
    validators.aadhaarNumber(false),
    validators.nameOnAadhaar(false),
    validators.dateOfBirth('dateOfBirth', false),
    validators.gender(false),
    validators.address(false),
    validators.panNumber(false),
    validators.panName(false),
    validators.dateOfBirth('panDateOfBirth', false),
    validators.specialties(15, true),
    validators.experience(false),
    validators.serviceAreas(10, false),
];

// Common parameter validators
const mongoIdValidator = validators.mongoId();

// Portfolio validators
const addPortfolioItemValidators = [validators.portfolioCaption()];

const updatePortfolioItemValidators = [
    validators.mongoId('portfolioItemId'),
    validators.portfolioCaption(true),
];

const deletePortfolioItemValidators = [validators.mongoId('portfolioItemId')];

module.exports = {
    userSignupValidators,
    userLoginValidators,
    refreshTokenValidator,
    updateProfileValidators,
    brokerApplicationValidators,
    contractorApplicationValidators,
    assetValidators,
    mongoIdValidator,
    forgotPasswordValidators,
    resetPasswordValidators,
    changePasswordValidators,
    createAssetValidators,
    getAssetsByEntityValidators,
    brokerUpdateValidators,
    contractorUpdateValidators,
    addPortfolioItemValidators,
    updatePortfolioItemValidators,
    deletePortfolioItemValidators,
};
