/**
 * User Profile Hook
 */

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { userService, UserProfile } from '@/services/user.service'

export function useUserProfile() {
  const { data: session, update: updateSession } = useSession()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchProfile = async () => {
    if (!session?.user?.accessToken) {
      setError('No access token available')
      return
    }

    setLoading(true)
    setError(null)

    try {
      console.log('Starting profile fetch...')
      console.log('Session user:', {
        id: session.user.id,
        email: session.user.email,
        hasAccessToken: !!session.user.accessToken,
      })

      // Try to get profile from the profile endpoint
      let result = await userService.getProfile(
        session.user.accessToken,
        session.user.id // Using user ID as session ID
      )

      // If profile endpoint fails, try root endpoint
      if (!result.success) {
        console.log('Profile endpoint failed, trying root endpoint...')
        console.log('Profile error:', result.error)

        result = await userService.getUserFromRoot(
          session.user.accessToken,
          session.user.id
        )
      }

      if (result.success && result.data) {
        console.log('Profile fetch successful:', result.data)
        setProfile(result.data)

        // Update the session with the latest user data
        try {
          await updateSession({
            ...session,
            user: {
              ...session.user,
              id: result.data.id,
              name: result.data.name,
              email: result.data.email,
              phone: result.data.phone,
              role: result.data.role,
              isVerified: result.data.isEmailVerified,
              isEmailVerified: result.data.isEmailVerified,
              isPhoneVerified: result.data.isPhoneVerified,
              location: result.data.location,
              isAvailable: result.data.isAvailable,
              partnershipRequest: result.data.partnershipRequest,
              avatar: result.data.avatar,
              createdAt: result.data.createdAt,
              updatedAt: result.data.updatedAt,
            },
          })
          console.log('Session updated successfully')
        } catch (sessionError) {
          console.error('Failed to update session:', sessionError)
          // Don't fail the whole operation if session update fails
        }
      } else {
        console.error('Both profile endpoints failed:', result.error)
        setError(result.error || 'Failed to fetch profile')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Error fetching profile:', err)
    } finally {
      setLoading(false)
    }
  }

  const refreshProfile = () => {
    fetchProfile()
  }

  // Fetch profile when session is available
  useEffect(() => {
    if (session?.user?.accessToken && !profile) {
      fetchProfile()
    }
  }, [session?.user?.accessToken])

  return {
    profile,
    loading,
    error,
    refreshProfile,
    fetchProfile,
  }
}
