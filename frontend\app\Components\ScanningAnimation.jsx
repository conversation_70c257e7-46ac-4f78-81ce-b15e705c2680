import React, { useEffect, useRef } from 'react';
import { View, Text, Animated, Modal } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const ScanningAnimation = ({ visible, onComplete, theme, styles, documentType = 'document' }) => {
    const scanLineAnim = useRef(new Animated.Value(0)).current;
    const pulseAnim = useRef(new Animated.Value(1)).current;
    const rotateAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        if (visible) {
            // Start scanning line animation
            const scanAnimation = Animated.loop(
                Animated.sequence([
                    Animated.timing(scanLineAnim, {
                        toValue: 1,
                        duration: 1500,
                        useNativeDriver: true,
                    }),
                    Animated.timing(scanLineAnim, {
                        toValue: 0,
                        duration: 1500,
                        useNativeDriver: true,
                    }),
                ])
            );

            // Start pulse animation
            const pulseAnimation = Animated.loop(
                Animated.sequence([
                    Animated.timing(pulseAnim, {
                        toValue: 1.2,
                        duration: 800,
                        useNativeDriver: true,
                    }),
                    Animated.timing(pulseAnim, {
                        toValue: 1,
                        duration: 800,
                        useNativeDriver: true,
                    }),
                ])
            );

            // Start rotation animation
            const rotateAnimation = Animated.loop(
                Animated.timing(rotateAnim, {
                    toValue: 1,
                    duration: 2000,
                    useNativeDriver: true,
                })
            );

            scanAnimation.start();
            pulseAnimation.start();
            rotateAnimation.start();

            // Auto complete after 5 seconds
            const timer = setTimeout(() => {
                scanAnimation.stop();
                pulseAnimation.stop();
                rotateAnimation.stop();
                onComplete();
            }, 5000);

            return () => {
                clearTimeout(timer);
                scanAnimation.stop();
                pulseAnimation.stop();
                rotateAnimation.stop();
            };
        }
    }, [visible, scanLineAnim, pulseAnim, rotateAnim, onComplete]);

    const scanLineTranslateY = scanLineAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [-50, 50],
    });

    const rotateInterpolate = rotateAnim.interpolate({
        inputRange: [0, 1],
        outputRange: ['0deg', '360deg'],
    });

    if (!visible) return null;

    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="fade"
            statusBarTranslucent={true}
        >
            <View style={styles.scanningOverlay}>
                <View style={[styles.scanningContainer, { backgroundColor: theme.CARD }]}>
                    {/* Document Icon with Pulse Animation */}
                    <Animated.View
                        style={{
                            transform: [{ scale: pulseAnim }],
                        }}
                    >
                        <MaterialCommunityIcons
                            name="card-account-details"
                            size={60}
                            color={theme.PRIMARY}
                        />
                    </Animated.View>

                    {/* Scanning Line */}
                    <View style={{
                        position: 'absolute',
                        top: 30,
                        left: 30,
                        right: 30,
                        height: 60,
                        overflow: 'hidden',
                    }}>
                        <Animated.View
                            style={{
                                position: 'absolute',
                                left: 0,
                                right: 0,
                                height: 2,
                                backgroundColor: theme.PRIMARY,
                                opacity: 0.8,
                                transform: [{ translateY: scanLineTranslateY }],
                            }}
                        />
                    </View>

                    {/* Rotating Scanner Icon */}
                    <Animated.View
                        style={{
                            position: 'absolute',
                            top: 20,
                            right: 20,
                            transform: [{ rotate: rotateInterpolate }],
                        }}
                    >
                        <MaterialCommunityIcons
                            name="radar"
                            size={20}
                            color={theme.SECONDARY}
                        />
                    </Animated.View>

                    <Text style={[styles.scanningText, { color: theme.TEXT_PRIMARY }]}>
                        Validating {documentType}...
                    </Text>
                    <Text style={[styles.scanningSubtext, { color: theme.TEXT_SECONDARY }]}>
                        Please wait while we verify your information
                    </Text>

                    {/* Progress Dots */}
                    <View style={{
                        flexDirection: 'row',
                        marginTop: 20,
                        justifyContent: 'center',
                    }}>
                        {[0, 1, 2].map((index) => (
                            <Animated.View
                                key={index}
                                style={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: 4,
                                    backgroundColor: theme.PRIMARY,
                                    marginHorizontal: 4,
                                    opacity: pulseAnim,
                                }}
                            />
                        ))}
                    </View>
                </View>
            </View>
        </Modal>
    );
};

export default ScanningAnimation;
