import { privateAPIClient } from '../index';

const API_PREFIX = '/site-service/api/v1';

export const createProject = async (projectData) => {
    try {
        const response = await privateAPIClient.post(
            `${API_PREFIX}/projects`,
            projectData
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to create project'
        );
    }
};

export const fetchUserProjects = async () => {
    try {
        const response = await privateAPIClient.get(`${API_PREFIX}/projects`);
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to fetch projects'
        );
    }
};

export const fetchProjectDetails = async (projectId) => {
    try {
        const response = await privateAPIClient.get(
            `${API_PREFIX}/projects/${projectId}`
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to fetch project details'
        );
    }
};

export const updateProject = async (projectId, projectData) => {
    try {
        const response = await privateAPIClient.patch(
            `${API_PREFIX}/projects/${projectId}`,
            projectData
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to update project'
        );
    }
};



export const createServiceRequest = async (projectId, serviceRequestData) => {
    try {
        const response = await privateAPIClient.post(
            `${API_PREFIX}/projects/${projectId}/service-requests`,
            serviceRequestData
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to create service request'
        );
    }
};

export const fetchServiceRequests = async () => {
    try {
        const response = await privateAPIClient.get(
            `${API_PREFIX}/service-requests`
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to fetch service requests'
        );
    }
};

export const updateServiceRequest = async (projectId, status) => {
    try {
        const response = await privateAPIClient.patch(
            `${API_PREFIX}/service-request/${projectId}`,
            { status }
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to update service request'
        );
    }
};

// Progress Management APIs
export const addProgressLog = async (projectId, progressData) => {
    try {
        console.log('=== API CALL DEBUG ===');
        console.log('Project ID:', projectId);
        console.log('Progress Data:', progressData);
        console.log('API Endpoint:', `${API_PREFIX}/projects/${projectId}/progress`);

        const response = await privateAPIClient.post(
            `${API_PREFIX}/projects/${projectId}/progress`,
            progressData
        );

        console.log('API Response:', response.data);
        return response.data;
    } catch (error) {
        console.log('API Error:', error);
        console.log('Error Response:', error.response?.data);
        console.log('Error Status:', error.response?.status);
        throw new Error(
            error.response?.data?.message || 'Failed to add progress log'
        );
    }
};

export const fetchProgressLogs = async (projectId) => {
    try {
        const response = await privateAPIClient.get(
            `${API_PREFIX}/projects/${projectId}/progress`
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to fetch progress logs'
        );
    }
};

export const updateProgressLog = async (projectId, logId, progressData) => {
    try {
        const response = await privateAPIClient.patch(
            `${API_PREFIX}/projects/${projectId}/progress/${logId}`,
            progressData
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to update progress log'
        );
    }
};

export const deleteProgressLog = async (projectId, logId) => {
    try {
        const response = await privateAPIClient.delete(
            `${API_PREFIX}/projects/${projectId}/progress/${logId}`
        );
        return response.data;
    } catch (error) {
        throw new Error(
            error.response?.data?.message || 'Failed to delete progress log'
        );
    }
};
