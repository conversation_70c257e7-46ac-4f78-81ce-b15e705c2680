const ExpressError = require('@build-connect/utils/ExpressError');
const mongoose = require('mongoose');
const Broker = require('../../model/Broker');
const Aadhaar = require('../../model/Aadhaar');
const PAN = require('../../model/Pan');
const Asset = require('../../model/Asset');
const User = require('../../model/user');

exports.getBrokerApplicationByUserId = async (req, res) => {
    const userId = req.user.id;

    const brokerProfile = await Broker.findOne({ user: userId }).lean();
    if (!brokerProfile) {
        throw new ExpressError('Broker application not found', 404);
    }

    const aadhaar = await Aadhaar.findOne({ userId }).lean();
    const pan = await PAN.findOne({ userId }).lean();

    const aadhaarAsset = aadhaar
        ? await Asset.findOne({
              entityId: aadhaar._id,
              entityType: 'Aadhaar',
          }).lean()
        : null;
    const panAsset = pan
        ? await Asset.findOne({
              entityId: pan._id,
              entityType: 'PAN',
          }).lean()
        : null;

    res.status(200).json({
        broker: {
            experience: brokerProfile.experience,
            serviceAreas: brokerProfile.serviceAreas,
            aadhaarNumber: aadhaar.aadhaarNumber,
            nameOnAadhaar: aadhaar.nameOnAadhaar,
            dateOfBirth: aadhaar.dateOfBirth,
            gender: aadhaar.gender,
            address: aadhaar.address,
            aadhaarAsset: aadhaarAsset ? aadhaarAsset.imageURL : null,
            panNumber: pan.panNumber,
            panName: pan.panName,
            panDateOfBirth: pan.dateOfBirth,
            panAsset: panAsset ? panAsset.imageURL : null,
            brokerId: brokerProfile._id,
            verificationStatus: brokerProfile.verificationStatus,
        },
    });
};

exports.getAllBrokers = async (req, res) => {
    try {
        // Get all verified brokers with their user information
        const brokers = await Broker.find({ verificationStatus: 'verified' })
            .populate('user', 'name isAvailable')
            .lean();

        // Get avatar assets for each broker
        const brokersWithAssets = await Promise.all(
            brokers.map(async (broker) => {
                const avatarAsset = await Asset.findOne({
                    entityId: broker.user._id,
                    entityType: 'User',
                    assetType: 'avatar',
                }).lean();

                return {
                    id: broker.user._id, 
                    profileId: broker._id, 
                    name: broker.user.name,
                    serviceAreas: broker.serviceAreas,
                    ratings: broker.ratings,
                    image: avatarAsset ? avatarAsset.imageURL : null,
                    isAvailable: broker.user.isAvailable,
                };
            })
        );

        res.status(200).json({
            success: true,
            brokers: brokersWithAssets,
        });
    } catch (error) {
        throw new ExpressError('Failed to fetch brokers', 500);
    }
};

exports.getBrokerProfile = async (req, res) => {
    const { brokerId } = req.params;

    try {
            broker = await Broker.findOne({ user: brokerId })
                .populate('user', 'name isAvailable')
                .lean();
        if (!broker) {
            throw new ExpressError(`Broker not found with ID: ${brokerId}`, 404);
        }

        // Get avatar asset for the broker
        const avatarAsset = await Asset.findOne({
            entityId: broker.user._id,
            entityType: 'User',
            assetType: 'avatar',
        }).lean();

        res.status(200).json({
            success: true,
            broker: {
                id: broker.user._id, // Use user ID instead of broker profile ID
                profileId: broker._id, // Keep profile ID for reference if needed
                name: broker.user.name,
                isAvailable: broker.user.isAvailable,
                experience: broker.experience,
                serviceAreas: broker.serviceAreas,
                ratings: broker.ratings,
                image: avatarAsset ? avatarAsset.imageURL : null,
                verificationStatus: broker.verificationStatus,
            },
        });
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Failed to fetch broker profile', 500);
    }
};
